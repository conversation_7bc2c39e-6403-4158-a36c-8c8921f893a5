import { extract<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils';
import { createStep, createWorkflow } from '@mastra/core/workflows';
import { CoreMessage, TextPart } from 'ai';
import { z } from 'zod';
import { videoScouter } from '@/services/scouting/videoScouter';

// Configuration constants for workflow operations
// Export for external access and modification
export const WORKFLOW_CONFIG = {
  MAX_VIDEOS_FOR_CONTEXT: 3,
  CREATOR_BATCH_SIZE: 100,
} as const;

/**
 * Extract essential video data for filtering (reduce payload size)
 * @param video Full video object
 * @returns Minimal video data for context
 */
function extractVideoEssentials(video: any) {
  return {
    video_id: video.video_id,
    description: video.description, // Contains title and hashtags
    view_count: video.view_count,
    like_count: video.like_count,
    comment_count: video.comment_count,
    share_count: video.share_count,
  };
}

// Schema for the hashtag search results
const searchHashtagSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  rationale: z.string().optional(),
});

// Schema for creator filtering results
const creatorFilterSchema = z.object({
  qualified_kols: z.array(
    z.object({
      unique_id: z.string(),
      collect_reason: z.string(),
    }),
  ),
});

export const creatorScoutWorkflow = createWorkflow({
  id: 'creator-scout',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
    useIntelligentChallengeSelection: z.boolean().optional().default(false),
    desiredCreatorCount: z.number().optional().default(50),
  }),
  outputSchema: z.object({
    desiredCreators: z.number(),
    scoutedCreators: z.number(),
    results: z.array(
      z.object({
        url: z.string().url(),
        reason: z.string(),
      }),
    ),
  }),
});

// Step 1: Analyze the user's requirements and generate hashtags
const analyzeRequirementStep = createStep({
  id: 'analyzeRequirement',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
    useIntelligentChallengeSelection: z.boolean().optional().default(false),
    desiredCreatorCount: z.number().optional().default(50),
  }),
  resumeSchema: z.object({
    userInputMessage: z.string().optional(),
    messages: z.array(z.any()),
  }),
  outputSchema: z.object({
    core: z.array(z.string()),
    adjacent: z.array(z.string()),
    rationale: z.string().optional(),
    targetCreatorDescription: z.string(), // Pass through
    useIntelligentChallengeSelection: z.boolean(), // Pass through
    desiredCreatorCount: z.number(), // Pass through
    scoutGuidance: z.string().optional(), // Scout's refined guidance
  }),
  execute: async ({ inputData, resumeData, suspend, mastra }) => {
    console.log('=== STEP EXECUTION START ===');
    // console.log('inputData', inputData);
    // console.log('resumeData', resumeData);

    const scout = mastra?.getAgent('creatorHashtagScout');
    if (!scout) {
      throw new Error('Campaign analyzer agent not found');
    }

    // Initialize messages array
    let messages: CoreMessage[] = [];

    // Check if this is a resume (either resumeData exists OR inputData has been contaminated with resume data)
    const isResume = resumeData?.messages || (inputData as any).messages;

    if (isResume) {
      // If we have cached messages, use them (this is a resume)
      console.log('Found cached messages, resuming conversation');

      // Get messages from resumeData first, fallback to inputData if framework merged them
      const cachedMessages =
        resumeData?.messages || (inputData as any).messages;
      messages = [...cachedMessages] as CoreMessage[]; // Create a copy to avoid mutation

      // Get user input message from resumeData first, fallback to inputData
      const userInputMessage =
        resumeData?.userInputMessage || (inputData as any).userInputMessage;

      // Add the user's response from the resumed workflow
      if (userInputMessage) {
        console.log('user responded:', userInputMessage);
        const userResponse: CoreMessage = {
          role: 'user',
          content: userInputMessage,
        };
        messages.push(userResponse);
      }
    } else {
      // If no cached messages, start a new conversation
      console.log('No cached messages found, starting new conversation');
      const description = inputData.targetCreatorDescription;
      const userDescription: CoreMessage = {
        role: 'user',
        content: description,
      };
      messages.push(userDescription);
    }

    console.log('About to call agent with messages:', messages.length);
    // Generate a response from the agent
    const resp = await scout.generate(messages);
    // console.log('Agent response received');
    const assistantMessage = resp.response.messages[0];
    const content = (assistantMessage.content as Array<TextPart>)[0];

    let parsedResult;
    // Check if the response is in the expected format
    try {
      // Extract the first JSON object from the response
      parsedResult = extractFirstJson(content.text);
      console.log('assistant response:', content.text);
      // console.log('parsedResult', parsedResult);
    } catch (e) {
      // JSON parsing failed, continue to suspend
    }

    const parseResult = searchHashtagSchema.safeParse(parsedResult);
    if (parseResult.success) {
      // console.log('parseResult.data', parseResult.data);
      return {
        ...parseResult.data,
        targetCreatorDescription: inputData.targetCreatorDescription,
        useIntelligentChallengeSelection:
          inputData.useIntelligentChallengeSelection ?? false,
        desiredCreatorCount: inputData.desiredCreatorCount ?? 50,
        scoutGuidance: parseResult.data.rationale || content.text, // Use rationale or full response as guidance
      };
    }

    // If not in expected format, add assistant message to conversation and suspend
    const updatedMessages = [...messages, assistantMessage];
    // console.log('appended messages', messages);
    // console.log('updatedMessages', updatedMessages);

    // console.log('before suspend, we are here');

    // Suspend and wait for user input
    // When resumed, the step will restart with the updated messages in resumeData
    await suspend({
      messages: updatedMessages,
      message: content,
    });

    // console.log('after suspend, we are here, returning empty hashtags');

    // This code should not execute in normal operation since suspend should restart the step
    // But we need to return something to satisfy TypeScript
    return {
      core: [],
      adjacent: [],
      targetCreatorDescription: inputData.targetCreatorDescription,
      useIntelligentChallengeSelection:
        inputData.useIntelligentChallengeSelection ?? false,
      desiredCreatorCount: inputData.desiredCreatorCount ?? 50,
      scoutGuidance: 'No guidance available',
    };
  },
});

// Step 2: Select challenges from keywords
const selectChallengesStep = createStep({
  id: 'select-challenges',
  inputSchema: z.object({
    core: z.array(z.string()),
    adjacent: z.array(z.string()),
    rationale: z.string().optional(),
    targetCreatorDescription: z.string(),
    useIntelligentChallengeSelection: z.boolean(),
    desiredCreatorCount: z.number(),
    scoutGuidance: z.string().optional(),
  }),
  outputSchema: z.object({
    challengeIds: z.array(z.string()),
    keywords: z.array(z.string()),
    targetCreatorDescription: z.string(), // Pass through
    useIntelligentChallengeSelection: z.boolean(), // Pass through
    desiredCreatorCount: z.number(), // Pass through
    scoutGuidance: z.string().optional(), // Pass through
  }),
  execute: async ({ inputData, mastra }) => {
    const {
      core,
      adjacent,
      targetCreatorDescription,
      useIntelligentChallengeSelection,
      desiredCreatorCount,
      scoutGuidance,
    } = inputData;

    const allKeywords = [...core, ...adjacent];
    console.log('Selecting challenges from keywords:', allKeywords);
    console.log(
      'Using intelligent selection:',
      useIntelligentChallengeSelection,
    );

    const challengeIds = await videoScouter.selectChallengesFromKeywords(
      allKeywords,
      {
        useIntelligentSelection: useIntelligentChallengeSelection,
        targetCreatorDescription,
        mastraInstance: mastra,
      },
    );

    console.log(
      `Selected ${challengeIds.length} challenges for creator scouting`,
    );

    return {
      challengeIds,
      keywords: allKeywords,
      targetCreatorDescription,
      useIntelligentChallengeSelection,
      desiredCreatorCount,
      scoutGuidance,
    };
  },
});

// Step 3: Scout creators from challenges
const scoutCreatorsStep = createStep({
  id: 'scout-creators',
  inputSchema: z.object({
    challengeIds: z.array(z.string()),
    keywords: z.array(z.string()),
    targetCreatorDescription: z.string(),
    useIntelligentChallengeSelection: z.boolean(),
    desiredCreatorCount: z.number(),
    scoutGuidance: z.string().optional(),
  }),
  outputSchema: z.object({
    creators: z.array(z.any()), // Enhanced creator data with videoCount
    videos: z.array(z.any()), // Creator videos (TiktokVideoSchema)
    stats: z.object({
      challengeVideosCollected: z.number(),
      uniqueCreatorsFound: z.number(),
      creatorsProcessed: z.number(),
      successfulCreators: z.number(),
      totalCreatorVideos: z.number(),
      totalVideosProcessed: z.number(),
    }),
    targetCreatorDescription: z.string(), // Pass through
    scoutGuidance: z.string().optional(), // Pass through
  }),
  execute: async ({ inputData }) => {
    const {
      challengeIds,
      desiredCreatorCount,
      targetCreatorDescription,
      scoutGuidance,
    } = inputData;

    console.log(`Scouting creators from ${challengeIds.length} challenges`);
    console.log(`Target creator count: ${desiredCreatorCount}`);

    if (challengeIds.length === 0) {
      console.warn('No challenges selected, returning empty results');
      return {
        creators: [],
        videos: [],
        stats: {
          challengeVideosCollected: 0,
          uniqueCreatorsFound: 0,
          creatorsProcessed: 0,
          successfulCreators: 0,
          totalCreatorVideos: 0,
          totalVideosProcessed: 0,
        },
        targetCreatorDescription,
        scoutGuidance,
      };
    }

    // Scout creators from each challenge and combine results
    const allCreators: any[] = [];
    const allVideos: any[] = [];
    let totalStats = {
      challengeVideosCollected: 0,
      uniqueCreatorsFound: 0,
      creatorsProcessed: 0,
      successfulCreators: 0,
      totalCreatorVideos: 0,
      totalVideosProcessed: 0,
    };

    // Calculate creators per challenge to reach desired total
    const creatorsPerChallenge = Math.ceil(
      desiredCreatorCount / challengeIds.length,
    );

    for (const challengeId of challengeIds) {
      try {
        console.log(`Scouting challenge: ${challengeId}`);
        const result = await videoScouter.scoutChallengeCreators(
          challengeId,
          creatorsPerChallenge,
        );

        allCreators.push(...result.creators);
        allVideos.push(...result.videos);

        // Accumulate stats
        totalStats.challengeVideosCollected +=
          result.stats.challengeVideosCollected;
        totalStats.uniqueCreatorsFound += result.stats.uniqueCreatorsFound;
        totalStats.creatorsProcessed += result.stats.creatorsProcessed;
        totalStats.successfulCreators += result.stats.successfulCreators;
        totalStats.totalCreatorVideos += result.stats.totalCreatorVideos;
        totalStats.totalVideosProcessed += result.stats.totalVideosProcessed;

        console.log(
          `Challenge ${challengeId} yielded ${result.creators.length} creators`,
        );
      } catch (error) {
        console.error(`Error scouting challenge ${challengeId}:`, error);
        // Continue with next challenge
      }
    }

    // Deduplicate creators by unique_id
    const creatorMap = new Map();
    const uniqueCreators = [];

    for (const creator of allCreators) {
      if (!creatorMap.has(creator.unique_id)) {
        creatorMap.set(creator.unique_id, creator);
        uniqueCreators.push(creator);
      }
    }

    // Limit to desired count
    const finalCreators = uniqueCreators.slice(0, desiredCreatorCount);

    console.log(
      `Final results: ${finalCreators.length} unique creators, ${allVideos.length} videos`,
    );

    return {
      creators: finalCreators,
      videos: allVideos,
      stats: totalStats,
      targetCreatorDescription,
      scoutGuidance,
    };
  },
});

// Step 4: Filter creators using the creator filter agent
const filterCreatorsStep = createStep({
  id: 'filter-creators',
  inputSchema: z.object({
    creators: z.array(z.any()), // Enhanced creator data with videoCount
    videos: z.array(z.any()), // Creator videos (TiktokVideoSchema)
    stats: z.object({
      challengeVideosCollected: z.number(),
      uniqueCreatorsFound: z.number(),
      creatorsProcessed: z.number(),
      successfulCreators: z.number(),
      totalCreatorVideos: z.number(),
      totalVideosProcessed: z.number(),
    }),
    targetCreatorDescription: z.string(), // Pass through from initial input
    scoutGuidance: z.string().optional(), // Scout's refined guidance
  }),
  outputSchema: z.object({
    desiredCreators: z.number(),
    scoutedCreators: z.number(),
    results: z.array(
      z.object({
        url: z.string().url(),
        reason: z.string(),
      }),
    ),
  }),
  execute: async ({ inputData, mastra }) => {
    const { creators, videos, targetCreatorDescription, scoutGuidance } =
      inputData;
    console.log(`Filtering ${creators.length} creators in batches`);
    console.log(`Using ${videos.length} creator videos for enhanced filtering`);
    console.log('Original requirements:', targetCreatorDescription);
    if (scoutGuidance) {
      console.log('Scout guidance:', scoutGuidance);
    }

    const creatorFilterAgent = mastra?.getAgent('creatorFilterAgent');
    if (!creatorFilterAgent) {
      throw new Error('Creator filter agent not found');
    }

    const batchSize = WORKFLOW_CONFIG.CREATOR_BATCH_SIZE;
    const allQualifiedCreators: Array<{
      unique_id: string;
      collect_reason: string;
    }> = [];

    // Create a map of creator videos for enhanced filtering (using essential data only)
    const creatorVideosMap = new Map<string, any[]>();
    for (const video of videos) {
      const creatorId = video.author.unique_id;
      if (!creatorVideosMap.has(creatorId)) {
        creatorVideosMap.set(creatorId, []);
      }
      // Store only essential video data to reduce payload size
      creatorVideosMap.get(creatorId)!.push(extractVideoEssentials(video));
    }

    // Process creators in batches
    for (let i = 0; i < creators.length; i += batchSize) {
      const batch = creators.slice(i, i + batchSize);
      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1} with ${batch.length} creators`,
      );

      try {
        // Enhance creator data with their videos for better filtering
        const enhancedBatch = batch.map((creator) => {
          const creatorVideos = creatorVideosMap.get(creator.unique_id) || [];
          return {
            ...creator,
            recentVideos: creatorVideos.slice(
              0,
              WORKFLOW_CONFIG.MAX_VIDEOS_FOR_CONTEXT,
            ), // Include recent videos for context
            totalVideosAnalyzed: creatorVideos.length,
          };
        });

        // Construct enhanced prompt with both original requirements and scout guidance
        let prompt = `Creator Requirements: ${targetCreatorDescription}`;

        if (scoutGuidance) {
          prompt += `\n\nScout Guidance: ${scoutGuidance}`;
        }

        prompt += `\n\nNote: Each creator now includes their recent video content for better assessment of their style and audience fit.`;
        prompt += `\n\nCreator Data: ${JSON.stringify(enhancedBatch, null, 2)}`;

        const userMessage: CoreMessage = {
          role: 'user',
          content: prompt,
        };

        const resp = await creatorFilterAgent.generate([userMessage]);
        const assistantMessage = resp.response.messages[0];
        const content = (assistantMessage.content as Array<TextPart>)[0];

        // Extract JSON from the response
        const parsedResult = extractFirstJson(content.text);
        const parseResult = creatorFilterSchema.safeParse(parsedResult);

        if (parseResult.success) {
          allQualifiedCreators.push(...parseResult.data.qualified_kols);
          console.log(
            `Batch ${Math.floor(i / batchSize) + 1} yielded ${parseResult.data.qualified_kols.length} qualified creators`,
          );
        } else {
          console.error(
            `Failed to parse response for batch ${Math.floor(i / batchSize) + 1}:`,
            parseResult.error,
          );
        }
      } catch (error) {
        console.error(
          `Error processing batch ${Math.floor(i / batchSize) + 1}:`,
          error,
        );
        // Continue with next batch
      }
    }

    console.log(
      `Total qualified creators found: ${allQualifiedCreators.length}`,
    );

    // Convert to the expected output format
    const results = allQualifiedCreators.map((creator) => ({
      url: `https://www.tiktok.com/@${creator.unique_id}`,
      reason: creator.collect_reason,
    }));

    console.log('results', results);

    return {
      desiredCreators: creators.length,
      scoutedCreators: allQualifiedCreators.length,
      results,
    };
  },
});

// Define the workflow steps and their relationships
creatorScoutWorkflow
  .then(analyzeRequirementStep)
  .then(selectChallengesStep)
  .then(scoutCreatorsStep)
  .then(filterCreatorsStep)
  .commit();
